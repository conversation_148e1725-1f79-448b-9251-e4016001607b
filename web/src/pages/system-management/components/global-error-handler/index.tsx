import React, { useEffect } from 'react';
import { useGlobalErrorHandler } from '../../hooks/use-global-error-handler';

interface GlobalErrorHandlerProps {
    children: React.ReactNode;
    showNetworkIndicator?: boolean;
}

/**
 * 全局错误处理组件
 * 初始化全局错误监听和处理机制
 */
const GlobalErrorHandler: React.FC<GlobalErrorHandlerProps> = ({
    children,
    showNetworkIndicator = true,
}) => {
    const { errorHandler, feedback, checkNetworkStatus } = useGlobalErrorHandler();

    useEffect(() => {
        // 初始化时检查网络状态
        if (!checkNetworkStatus()) {
            console.warn('Initial network check failed');
        }

        // 在开发环境下添加调试信息
        if (process.env.NODE_ENV === 'development') {
            console.log('Global error handler initialized');
        }
    }, [checkNetworkStatus]);

    return (
        <>
            {children}
        </>
    );
};

export default GlobalErrorHandler;