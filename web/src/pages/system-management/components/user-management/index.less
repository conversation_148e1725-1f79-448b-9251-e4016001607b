@import '../../../../theme/vars.less';

.user-management {
    width: 100%;
    height: 100%;
        display: flex;
        flex-direction: column;
    
    .user-management-content {
        width: 100%;
        flex: 1;
            padding: 16px;
            min-height: 0;
        
            @media screen and (min-height: 769px) {
                overflow-y: auto;
            }
        }
    
                .user-management-header {
        margin-bottom: 24px;
    }
}

// 用户管理表格组件样式
.user-management-table {
    .search-bar {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        .search-input {
            .ant-input-search-button {
                border-radius: 0 6px 6px 0;
            }
        }
        
        .filter-select {
            .ant-select-selector {
                border-radius: 6px;
            }
        }
    }
    
    .table-container {
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;

        &:hover {
            background: #999;
        }
    }

    ::-webkit-scrollbar-track {
        background: transparent;
    }

    .ant-table-body {
        overflow-y: overlay !important;

        &:hover {
            &::-webkit-scrollbar-thumb {
                background: #999;
            }
        }
    }
    }
}

// 用户编辑模态框样式
.user-edit-modal {
    // 只读邮箱输入框样式
    .readonly-email-input {
        &.ant-input {
            background-color: #f5f5f5 !important;
            color: #666666 !important;
            cursor: not-allowed !important;
            
            // 确保输入框内部所有元素都有相同背景
            input {
                background-color: #f5f5f5 !important;
                color: #666666 !important;
                cursor: not-allowed !important;
            }
            
            // 覆盖可能的伪类样式
            &:hover,
            &:focus,
            &:active {
                background-color: #f5f5f5 !important;
                border-color: #d9d9d9 !important;
                box-shadow: none !important;
            }
            
            // 确保前缀图标区域也是灰色背景
            .ant-input-prefix {
                background-color: transparent !important;
                color: #8c8c8c !important;
            }
        }
    }
}