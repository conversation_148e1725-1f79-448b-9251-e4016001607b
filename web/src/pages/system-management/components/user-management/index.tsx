import React, { useState } from 'react';
import UserTable from './user-table';
import UserModal from './user-modal';
import TenantDetailsModal from './tenant-details-modal';
import TeamDetailsModal from './team-details-modal';
import { useUserManagement } from './hooks/use-user-management';
import { useErrorHandler } from '../../hooks/use-error-handler';
import { useFeedback } from '../../hooks/use-feedback';
import ErrorBoundary from '../error-boundary';
import './index.less';

const UserManagement: React.FC = () => {
    const errorHandler = useErrorHandler({
        showNotification: true,
        autoClose: true,
        duration: 4.5,
    });
    const feedback = useFeedback();

    const {
        users,
        loading,
        pagination,
        filters,
        selectedUser,
        editModalVisible,
        handleSearch,
        handleStatusFilter,
        handlePageChange,
        handleEditUser,
        handleUpdateUser,
        handleToggleUserStatus,
        handleCloseEditModal,
        refreshUsers,
    } = useUserManagement();

    // 详情模态框状态
    const [tenantDetailsVisible, setTenantDetailsVisible] = useState(false);
    const [teamDetailsVisible, setTeamDetailsVisible] = useState(false);
    const [selectedUserForDetails, setSelectedUserForDetails] = useState<any>(null);

    // 处理用户状态切换的确认
    const handleUserStatusToggle = (user: any) => {
        const action = user.status === '1' ? '禁用' : '激活';

        feedback.showStatusChangeConfirm(
            action,
            user.nickname || user.email,
            async () => {
                try {
                    await handleToggleUserStatus(user);
                    feedback.operationSuccess(action, user.nickname || user.email);
                } catch (error) {
                    errorHandler.handleError(error);
                    feedback.operationError(action, user.nickname || user.email);
                }
            }
        );
    };

    // 处理查看租户详情
    const handleViewTenantDetails = (user: any) => {
        setSelectedUserForDetails(user);
        setTenantDetailsVisible(true);
    };

    // 处理查看团队详情
    const handleViewTeamDetails = (user: any) => {
        setSelectedUserForDetails(user);
        setTeamDetailsVisible(true);
    };

    // 关闭租户详情模态框
    const handleCloseTenantDetails = () => {
        setTenantDetailsVisible(false);
        setSelectedUserForDetails(null);
    };

    // 关闭团队详情模态框
    const handleCloseTeamDetails = () => {
        setTeamDetailsVisible(false);
        setSelectedUserForDetails(null);
    };

    // 处理用户信息更新
    const handleUserUpdate = async (values: any) => {
        try {
            feedback.dataSaving('用户信息');
            await handleUpdateUser(values);
            feedback.clearAll(); // 清除"正在保存"状态
            feedback.operationSuccess('更新', selectedUser?.nickname || selectedUser?.email);
        } catch (error) {
            feedback.clearAll(); // 清除"正在保存"状态
            errorHandler.handleError(error);
            // 移除重复的错误反馈，因为errorHandler已经处理了错误显示
            throw error; // 重新抛出错误，让模态框保持打开状态
        }
    };

    // 处理刷新操作
    const handleRefresh = async () => {
        try {
            feedback.dataLoading('用户列表');
            await refreshUsers();
            feedback.clearAll();
        } catch (error) {
            errorHandler.handleError(error);
        }
    };

    return (
        <ErrorBoundary
            title="用户管理模块错误"
            description="用户管理功能遇到问题，请尝试刷新或联系管理员"
            onError={(error, errorInfo) => {
                errorHandler.handleError({
                    type: 'UNKNOWN_ERROR' as any,
                    message: '用户管理组件错误',
                    details: { error, errorInfo },
                });
            }}
        >
            <div className="user-management">
                <div className="user-management-content">
                    <UserTable
                        users={users || []}
                        loading={loading || false}
                        pagination={pagination || { current: 1, pageSize: 20, total: 0 }}
                        filters={filters || { search: '', status: 'all' }}
                        onSearch={handleSearch}
                        onStatusFilter={handleStatusFilter}
                        onPageChange={handlePageChange}
                        onEditUser={handleEditUser}
                        onToggleUserStatus={handleUserStatusToggle}
                        onViewTenantDetails={handleViewTenantDetails}
                        onViewTeamDetails={handleViewTeamDetails}
                        onRefresh={handleRefresh}
                    />
                </div>

                <UserModal
                    visible={editModalVisible}
                    user={selectedUser}
                    loading={loading}
                    onOk={handleUserUpdate}
                    onCancel={handleCloseEditModal}
                />

                {/* 租户详情模态框 */}
                <TenantDetailsModal
                    visible={tenantDetailsVisible}
                    user={selectedUserForDetails}
                    onCancel={handleCloseTenantDetails}
                />

                {/* 团队详情模态框 */}
                <TeamDetailsModal
                    visible={teamDetailsVisible}
                    user={selectedUserForDetails}
                    onCancel={handleCloseTeamDetails}
                />
            </div>
        </ErrorBoundary>
    );
};

export default UserManagement;